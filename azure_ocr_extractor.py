"""
Azure OCR JSON Key-Value Extractor using Polygon Analysis

This module provides functionality to extract key-value pairs from Azure OCR JSON
output by analyzing the spatial relationships between text elements using their
polygon coordinates.
"""

import json
import math
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum


class SpatialRelation(Enum):
    """Defines spatial relationships between text elements"""
    RIGHT_OF = "right_of"
    BELOW = "below"
    ABOVE = "above"
    LEFT_OF = "left_of"
    OVERLAPPING = "overlapping"


@dataclass
class TextElement:
    """Represents a text element with its content and polygon coordinates"""
    text: str
    polygon: List[Tuple[float, float]]
    confidence: float
    bounding_box: Tuple[float, float, float, float]  # (x_min, y_min, x_max, y_max)
    
    def __post_init__(self):
        """Calculate bounding box from polygon if not provided"""
        if not self.bounding_box:
            x_coords = [point[0] for point in self.polygon]
            y_coords = [point[1] for point in self.polygon]
            self.bounding_box = (
                min(x_coords), min(y_coords),
                max(x_coords), max(y_coords)
            )


@dataclass
class KeyValuePair:
    """Represents an extracted key-value pair"""
    key: str
    value: str
    key_element: TextElement
    value_element: TextElement
    confidence: float
    spatial_relation: SpatialRelation


class AzureOCRExtractor:
    """Main class for extracting key-value pairs from Azure OCR JSON"""
    
    def __init__(self, proximity_threshold: float = 50.0, confidence_threshold: float = 0.5):
        """
        Initialize the extractor
        
        Args:
            proximity_threshold: Maximum distance for considering elements as related
            confidence_threshold: Minimum confidence score for text elements
        """
        self.proximity_threshold = proximity_threshold
        self.confidence_threshold = confidence_threshold
    
    def parse_azure_ocr_json(self, ocr_json: Dict[str, Any]) -> List[TextElement]:
        """
        Parse Azure OCR JSON and extract text elements with polygons
        
        Args:
            ocr_json: Azure OCR JSON response
            
        Returns:
            List of TextElement objects
        """
        text_elements = []
        
        # Handle different Azure OCR response formats
        if 'analyzeResult' in ocr_json:
            # Form Recognizer format
            pages = ocr_json['analyzeResult'].get('pages', [])
        elif 'pages' in ocr_json:
            # Direct pages format
            pages = ocr_json['pages']
        else:
            raise ValueError("Unsupported Azure OCR JSON format")
        
        for page in pages:
            # Extract lines or words
            lines = page.get('lines', [])
            words = page.get('words', [])
            
            # Process lines first (usually more reliable for key-value extraction)
            for line in lines:
                text = line.get('content', line.get('text', ''))
                polygon = line.get('polygon', line.get('boundingPolygon', []))
                confidence = line.get('confidence', 1.0)
                
                if confidence >= self.confidence_threshold and text.strip():
                    # Convert polygon format if needed
                    if isinstance(polygon[0], dict):
                        polygon = [(p['x'], p['y']) for p in polygon]
                    elif isinstance(polygon[0], list):
                        polygon = [(p[0], p[1]) for p in polygon]
                    
                    text_elements.append(TextElement(
                        text=text.strip(),
                        polygon=polygon,
                        confidence=confidence,
                        bounding_box=None  # Will be calculated in __post_init__
                    ))
        
        return text_elements
    
    def calculate_distance(self, element1: TextElement, element2: TextElement) -> float:
        """Calculate distance between two text elements"""
        x1, y1, x2, y2 = element1.bounding_box
        x3, y3, x4, y4 = element2.bounding_box
        
        # Calculate center points
        center1 = ((x1 + x2) / 2, (y1 + y2) / 2)
        center2 = ((x3 + x4) / 2, (y3 + y4) / 2)
        
        return math.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)
    
    def get_spatial_relation(self, key_element: TextElement, value_element: TextElement) -> SpatialRelation:
        """Determine spatial relationship between key and value elements"""
        key_box = key_element.bounding_box
        value_box = value_element.bounding_box
        
        # Check for overlap
        if (key_box[0] < value_box[2] and key_box[2] > value_box[0] and
            key_box[1] < value_box[3] and key_box[3] > value_box[1]):
            return SpatialRelation.OVERLAPPING
        
        # Calculate relative positions
        key_center_x = (key_box[0] + key_box[2]) / 2
        key_center_y = (key_box[1] + key_box[3]) / 2
        value_center_x = (value_box[0] + value_box[2]) / 2
        value_center_y = (value_box[1] + value_box[3]) / 2
        
        # Determine primary relationship
        horizontal_diff = abs(value_center_x - key_center_x)
        vertical_diff = abs(value_center_y - key_center_y)
        
        if horizontal_diff > vertical_diff:
            # Horizontal relationship is stronger
            if value_center_x > key_center_x:
                return SpatialRelation.RIGHT_OF
            else:
                return SpatialRelation.LEFT_OF
        else:
            # Vertical relationship is stronger
            if value_center_y > key_center_y:
                return SpatialRelation.BELOW
            else:
                return SpatialRelation.ABOVE
    
    def is_likely_key(self, text: str) -> bool:
        """Determine if text is likely to be a key (label)"""
        # Common patterns for keys
        key_indicators = [
            text.endswith(':'),
            text.endswith('?'),
            len(text.split()) <= 4,  # Keys are usually short
            any(word.lower() in ['name', 'date', 'number', 'id', 'code', 'type', 'status'] 
                for word in text.lower().split()),
        ]
        
        return any(key_indicators)
    
    def extract_key_value_pairs(self, text_elements: List[TextElement]) -> List[KeyValuePair]:
        """
        Extract key-value pairs from text elements using spatial analysis
        
        Args:
            text_elements: List of TextElement objects
            
        Returns:
            List of KeyValuePair objects
        """
        key_value_pairs = []
        used_elements = set()
        
        # Sort elements by position (top to bottom, left to right)
        sorted_elements = sorted(text_elements, key=lambda e: (e.bounding_box[1], e.bounding_box[0]))
        
        for i, potential_key in enumerate(sorted_elements):
            if i in used_elements or not self.is_likely_key(potential_key.text):
                continue
            
            best_value = None
            best_distance = float('inf')
            best_relation = None
            
            # Look for nearby value elements
            for j, potential_value in enumerate(sorted_elements):
                if i == j or j in used_elements:
                    continue
                
                distance = self.calculate_distance(potential_key, potential_value)
                
                if distance <= self.proximity_threshold and distance < best_distance:
                    relation = self.get_spatial_relation(potential_key, potential_value)
                    
                    # Prefer values to the right or below keys
                    if relation in [SpatialRelation.RIGHT_OF, SpatialRelation.BELOW]:
                        best_value = potential_value
                        best_distance = distance
                        best_relation = relation
            
            if best_value:
                # Calculate combined confidence
                combined_confidence = (potential_key.confidence + best_value.confidence) / 2
                
                key_value_pairs.append(KeyValuePair(
                    key=potential_key.text,
                    value=best_value.text,
                    key_element=potential_key,
                    value_element=best_value,
                    confidence=combined_confidence,
                    spatial_relation=best_relation
                ))
                
                used_elements.add(i)
                used_elements.add(sorted_elements.index(best_value))
        
        return key_value_pairs
    
    def process_ocr_json(self, ocr_json: Dict[str, Any]) -> List[KeyValuePair]:
        """
        Main method to process Azure OCR JSON and extract key-value pairs
        
        Args:
            ocr_json: Azure OCR JSON response
            
        Returns:
            List of extracted KeyValuePair objects
        """
        text_elements = self.parse_azure_ocr_json(ocr_json)
        return self.extract_key_value_pairs(text_elements)


def main():
    """Example usage of the AzureOCRExtractor"""
    # Example usage
    extractor = AzureOCRExtractor(proximity_threshold=100.0, confidence_threshold=0.7)
    
    # Load your Azure OCR JSON file
    # with open('ocr_result.json', 'r') as f:
    #     ocr_data = json.load(f)
    # 
    # key_value_pairs = extractor.process_ocr_json(ocr_data)
    # 
    # for pair in key_value_pairs:
    #     print(f"Key: {pair.key} | Value: {pair.value} | Confidence: {pair.confidence:.2f}")


if __name__ == "__main__":
    main()
